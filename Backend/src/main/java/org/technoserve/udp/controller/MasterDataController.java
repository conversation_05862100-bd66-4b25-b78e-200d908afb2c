package org.technoserve.udp.controller;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.technoserve.udp.dto.ApiResponse;
import org.technoserve.udp.dto.CurrencyDTO;
import org.technoserve.udp.dto.MasterDistrictDTO;
import org.technoserve.udp.dto.MasterDistrictRequest;
import org.technoserve.udp.dto.MasterStateDTO;
import org.technoserve.udp.service.MasterDataService;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/master-data")
public class MasterDataController {

  private final MasterDataService masterDataService;

  @GetMapping("/states/{countryName}")
  public ResponseEntity<List<MasterStateDTO>> getStatesByCountry(@PathVariable String countryName) {
    return ResponseEntity.ok(masterDataService.getStatesByCountry(countryName));
  }

  @GetMapping("/districts/{stateId}")
  public ResponseEntity<List<MasterDistrictDTO>> getDistrictsByState(@PathVariable Long stateId) {
    return ResponseEntity.ok(masterDataService.getDistrictsByState(stateId));
  }

  @PostMapping("/districts/create")
  public ResponseEntity<ApiResponse> createDistrict(@Valid @RequestBody MasterDistrictRequest request) {
    return ResponseEntity.status(201).body(masterDataService.createDistrict(request));
  }

  /**
   * Get all currencies
   *
   * @return List of currency DTOs
   */
  @GetMapping("/currencies")
  public ResponseEntity<List<CurrencyDTO>> getAllCurrencies() {
    return ResponseEntity.ok(masterDataService.getAllCurrencies());
  }
}
