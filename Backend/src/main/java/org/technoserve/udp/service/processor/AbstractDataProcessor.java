package org.technoserve.udp.service.processor;

import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.technoserve.udp.dto.ValidationResult;
import org.technoserve.udp.entity.dataflow.ExcelFileMetaData;
import org.technoserve.udp.entity.program.Partner;
import org.technoserve.udp.entity.program.Program;
import org.technoserve.udp.service.processor.validator.EntityValidator;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Abstract base class for data processors
 *
 * @param <T> The entity type
 */
public abstract class AbstractDataProcessor<T> implements DataProcessor {

  protected static final Logger logger = LoggerFactory.getLogger(AbstractDataProcessor.class);

  protected final EntityValidator<T> validator;

  protected final FormulaEvaluator evaluator;

  protected AbstractDataProcessor(EntityValidator<T> validator, FormulaEvaluator  evaluator) {
    this.validator = validator;
    this.evaluator = evaluator;
  }


  @Override
  public List<ValidationResult> validateDataInParallel(Sheet sheet, Map<Integer, String> columnIndices, Map<String, String> mappings,
                                                       Program program, Partner partner, ExcelFileMetaData excelFileMetaData, ExecutorService executor) throws ExecutionException, InterruptedException {
    // Use CopyOnWriteArrayList for thread-safe results collection
    List<ValidationResult> results = new CopyOnWriteArrayList<>();

    // Create a list of futures for all row validation tasks
    List<Future<?>> futures = new ArrayList<>();

    // Start from row 1 (skip header)
    for (int i = 1; i <= sheet.getLastRowNum(); i++) {
      final int rowIndex = i;

      Row row = sheet.getRow(rowIndex);

      if (row == null || isRowEmpty(row)) {
        continue; // skip empty rows
      }

      futures.add(executor.submit(() -> {
        ValidationResult result = validateRow(sheet, rowIndex, columnIndices, mappings, program, partner, excelFileMetaData);
        results.add(result);
      }));
    }

    // Wait for all tasks to complete
    for (Future<?> future : futures) {
      future.get();
    }

    // Sort results by row index
    return results.stream()
        .sorted((r1, r2) -> Integer.compare(r1.getRowIndex(), r2.getRowIndex()))
        .toList();
  }

  /**
   * Validate a single row
   *
   * @param sheet         The Excel sheet
   * @param rowIndex      The row index
   * @param columnIndices Map of column indices to column names
   * @param mappings      Map of Excel column names to entity field names
   * @param program       The program
   * @param partner       The partner
   * @return The validation result for this row
   */
  protected ValidationResult validateRow(Sheet sheet, int rowIndex, Map<Integer, String> columnIndices,
                                         Map<String, String> mappings, Program program, Partner partner, ExcelFileMetaData excelFileMetaData) {
    Row row = sheet.getRow(rowIndex);
    if (row == null) {
      return ValidationResult.builder()
          .rowIndex(rowIndex)
          .valid(false)
          .errorMessage("Empty row")
          .build();
    }

    // Create a new entity for validation
    T entity = createEntity(program, partner, excelFileMetaData);

    // Map of validation errors for this row
    Map<String, String> validationErrors = new HashMap<>();

    // First, validate that all required fields are mapped
    validator.validateRequiredFieldMappings(mappings, validationErrors);

    // Process ID field if applicable
    processIdField(row, columnIndices, entity, validationErrors, mappings);

    // First, collect all values from the row into a HashMap
    Map<String, String> columnValues = getColumnValueMappings(row, columnIndices);

    // Check if the row has any data
    boolean rowHasData = !columnValues.isEmpty();

    // Iterate through mappings to validate and set field values
    for (Map.Entry<String, String> entry : mappings.entrySet()) {
      String fieldName = entry.getKey();
      String excelColumn = entry.getValue();

      if (excelColumn == null) continue; // Skip if no mapping exists

      // Check if this is a required field but no value exists
      if (!columnValues.containsKey(excelColumn)) {
        if (validator.isRequiredField(fieldName)) {
          validationErrors.put(excelColumn, fieldName + " is required");
        }
        continue;
      }

      String cellValue = columnValues.get(excelColumn);

      // Validate and set the field value
      try {
        // Use reflection to set the field value based on its type
        java.lang.reflect.Field field = entity.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        Class<?> fieldType = field.getType();

        // Try to convert the cell value to the appropriate type
        try {
          Object convertedValue = convertToFieldType(cellValue, fieldType, program, partner);
          if (convertedValue == null) {
            validationErrors.put(excelColumn, "Invalid format for " + fieldName);
          } else {
            field.set(entity, convertedValue);
          }
        } catch (Exception e) {
          validationErrors.put(excelColumn, "Invalid format for " + fieldName + ": " + e.getMessage());
        }
      } catch (NoSuchFieldException e) {
        validationErrors.put(excelColumn, "Field " + fieldName + " not found in " + entity.getClass().getSimpleName());
      } catch (Exception e) {
        validationErrors.put(excelColumn, "Error setting field " + fieldName + ": " + e.getMessage());
      }
    }

    // Apply entity-specific basic validations
    validator.validate(entity, validationErrors);

    // Apply entity-specific custom validations
    validator.performCustomValidations(entity, validationErrors);

    // Create validation result
    boolean isValid = validationErrors.isEmpty() && rowHasData;
    String errorMessage = isValid ? null : String.join("; ", validationErrors.values());

    return ValidationResult.builder()
        .rowIndex(rowIndex)
        .valid(isValid)
        .errorMessage(errorMessage)
        .entity(entity)
        .build();
  }

  /**
   * Create a new entity instance
   *
   * @param program The program
   * @param partner The partner
   * @return A new entity instance
   */
  protected abstract T createEntity(Program program, Partner partner, ExcelFileMetaData excelFileMetaData);

  /**
   * Process the ID field for the entity
   *
   * @param row              The Excel row
   * @param columnIndices    Map of column indices to column names
   * @param entity           The entity to update
   * @param validationErrors Map to store validation errors
   */
  protected abstract void processIdField(Row row, Map<Integer, String> columnIndices, T entity, Map<String, String> validationErrors, Map<String, String> mappings);

  /**
   * Get cell value as string regardless of cell type
   *
   * @param cell The cell
   * @return The cell value as string
   */
  private String getCellValueAsString(Cell cell) {
    if (cell == null) {
      return "";
    }

    CellType cellType = cell.getCellType();
    if (cellType == CellType.FORMULA) {
      CellValue evaluatedValue = evaluator.evaluate(cell);
      if (evaluatedValue == null) {
        return "";
      }

      return switch (evaluatedValue.getCellType()) {
        case STRING -> evaluatedValue.getStringValue();
        case NUMERIC -> DateUtil.isCellDateFormatted(cell)
            ? formatDate(cell.getDateCellValue())
            : String.valueOf(evaluatedValue.getNumberValue());
        case BOOLEAN -> String.valueOf(evaluatedValue.getBooleanValue());
        case ERROR -> "ERROR(" + evaluatedValue.getErrorValue() + ")";
        case BLANK -> "";
        default -> "";
      };
    }

    return switch (cellType) {
      case STRING -> cell.getStringCellValue();
      case NUMERIC -> DateUtil.isCellDateFormatted(cell)
          ? formatDate(cell.getDateCellValue())
          : new org.apache.poi.ss.usermodel.DataFormatter().formatCellValue(cell);
      case BOOLEAN -> String.valueOf(cell.getBooleanCellValue());
      case ERROR -> "ERROR(" + cell.getErrorCellValue() + ")";
      case BLANK, _NONE -> "";
      default -> "";
    };
  }

  private String formatDate(Date date) {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    return sdf.format(date);
  }


  @Override
  public int processDataInParallel(Sheet sheet, Map<Integer, String> columnIndices, Map<String, String> mappings,
                                   Program program, Partner partner, ExcelFileMetaData excelFileMetaData, int batchSize, ExecutorService executor) throws ExecutionException, InterruptedException {
    AtomicInteger recordsProcessed = new AtomicInteger(0);

    // Calculate number of rows to process
    int totalRows = sheet.getLastRowNum();

    // Calculate number of batches
    int numBatches = (totalRows + batchSize - 1) / batchSize;

    // Process each batch in parallel
    List<Future<Integer>> futures = new ArrayList<>();

    for (int batchIndex = 0; batchIndex < numBatches; batchIndex++) {
      final int startRow = batchIndex * batchSize + 1; // Skip header row
      final int endRow = Math.min(startRow + batchSize - 1, totalRows);

      futures.add(executor.submit(() -> processBatch(sheet, startRow, endRow, columnIndices, mappings, program, partner, excelFileMetaData)));
    }

    // Collect results
    for (Future<Integer> future : futures) {
      recordsProcessed.addAndGet(future.get());
    }

    return recordsProcessed.get();
  }

  protected abstract int processBatch(Sheet sheet, int startRow, int endRow, Map<Integer, String> columnIndices, Map<String, String> mappings, Program program, Partner partner, ExcelFileMetaData excelFileMetaData);


  /**
   * Convert a string value to the appropriate field type
   *
   * @param value      The string value to convert
   * @param targetType The target type to convert to
   * @return The converted value, or null if conversion fails
   */
  protected Object convertToFieldType(String value, Class<?> targetType, Program program, Partner partner) {
    try {
      if (targetType == String.class) {
        return value;
      } else if (targetType == Integer.class || targetType == int.class) {
        return Integer.parseInt(value);
      } else if (targetType == Long.class || targetType == long.class) {
        return Long.parseLong(value);
      } else if (targetType == Double.class || targetType == double.class) {
        return Double.parseDouble(value);
      } else if (targetType == Float.class || targetType == float.class) {
        return Float.parseFloat(value);
      } else if (targetType == Boolean.class || targetType == boolean.class) {
        return Boolean.parseBoolean(value);
      } else if (targetType == BigDecimal.class) {
        return new BigDecimal(value);
      } else if (targetType == LocalDate.class) {
        return parseDate(value);
      } else if (targetType == LocalDateTime.class) {
        return LocalDateTime.parse(value);
      } else {
        return customConvertToFieldType(value, targetType, program, partner);
      }
    } catch (Exception e) {
      logger.warn("Error converting value '{}' to type {}: {}", value, targetType.getName(), e.getMessage());
      return null;
    }
  }

  protected Object customConvertToFieldType(String value, Class<?> targetType, Program program, Partner partner) {
    logger.warn("Unsupported field type: {} for value:{}", targetType.getName(), value);
    return null;
  }

  private boolean isRowEmpty(Row row) {
    for (Cell cell : row) {
      if (cell.getCellType() != CellType.BLANK &&
          (cell.getCellType() != CellType.STRING || !cell.getStringCellValue().trim().isEmpty())) {
        return false;
      }
    }
    return true;
  }

  protected Map<String,String> getColumnValueMappings(Row row, Map<Integer, String> columnIndices) {
    Map<String, String> columnValues = new HashMap<>();
    for (int j = 0; j < row.getLastCellNum(); j++) {
      String columnName = columnIndices.get(j);
      if (columnName == null) continue;

      Cell cell = row.getCell(j);
      if (cell == null) continue;

      String cellValue = getCellValueAsString(cell);
      if (cellValue == null || cellValue.isEmpty()) continue;

      columnValues.put(columnName, cellValue);
    }
    return columnValues;
  }

  protected void mapExcelColumnsToFields(Object entity, Class<?> clazz, Map<String, String> mappings,
                                       Map<String, String> columnValues, Program program, Partner partner) {
    mappings.forEach((fieldName, excelColumn) -> {
      if (excelColumn == null || !columnValues.containsKey(excelColumn)) return;

      String cellValue = columnValues.get(excelColumn);
      try {
        // Use reflection to get the field from the entity's class
        java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
        field.setAccessible(true);

        // Convert the cell value to the appropriate type
        Object convertedValue = convertToFieldType(cellValue, field.getType(), program, partner);
        if (convertedValue != null) {
          // Set the converted value to the field in the entity
          field.set(entity, convertedValue);
          logger.debug("Set field {} to value {}", fieldName, convertedValue);
        }
      } catch (NoSuchFieldException e) {
        logger.error("Field {} not found in class {}", fieldName, clazz.getSimpleName(), e);
      } catch (IllegalAccessException e) {
        logger.error("Cannot access field {} in class {}", fieldName, clazz.getSimpleName(), e);
      } catch (Exception e) {
        logger.error("Unexpected error setting field {} in class {}", fieldName, clazz.getSimpleName(), e);
      }
    });
  }
  protected LocalDate parseDate(String dateStr) {
    // Try different date formats
    String[] dateFormats = {
        "yyyy-MM-dd", "dd-MM-yyyy", "dd/MM/yyyy", "yyyy/MM/dd"
    };

    for (String format : dateFormats) {
      try {
        return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(format));
      } catch (DateTimeParseException ignored) {
        // Try next format
      }
    }

    // If all formats fail, throw exception
    throw new DateTimeParseException("Unable to parse date: " + dateStr, dateStr, 0);
  }

}
