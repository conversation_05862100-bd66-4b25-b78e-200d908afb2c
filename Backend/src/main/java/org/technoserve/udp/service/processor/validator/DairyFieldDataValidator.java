package org.technoserve.udp.service.processor.validator;

import org.technoserve.udp.entity.dataflow.DairyFieldData;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * Validator for DairyFieldData entities
 */
public class DairyFieldDataValidator extends AbstractEntityValidator<DairyFieldData> {

    private static final Set<String> REQUIRED_FIELDS = new HashSet<>();

    static {
        REQUIRED_FIELDS.add("date");
        REQUIRED_FIELDS.add("totalFarmers");
        REQUIRED_FIELDS.add("noOfAnimalWelfareFarms");
        REQUIRED_FIELDS.add("noOfWomenEmpowerment");
    }

    @Override
    public void validate(DairyFieldData dairyFieldData, Map<String, String> validationErrors) {
        // Validate required fields
        if (dairyFieldData.getDate() == null) {
            validationErrors.put("Date", "Date is required");
        }

        if (dairyFieldData.getTotalFarmers() == null) {
            validationErrors.put("Total Farmers", "Total Farmers is required");
        }

        if (dairyFieldData.getNoOfAnimalWelfareFarms() == null) {
            validationErrors.put("No of Animal Welfare Farms", "No of Animal Welfare Farms is required");
        }

        if (dairyFieldData.getNoOfWomenEmpowerment() == null) {
            validationErrors.put("No of Women Empowerment", "No of Women Empowerment is required");
        }

    }

    @Override
    public void performCustomValidations(DairyFieldData dairyFieldData, Map<String, String> validationErrors) {

        // Validate that Total Farmers is a positive number
        if (dairyFieldData.getTotalFarmers() != null && dairyFieldData.getTotalFarmers() < 0) {
            validationErrors.put("Total Farmers", "Total Farmers must be a positive number");
        }

        // Validate that Animal Welfare Farms is a positive number
        if (dairyFieldData.getNoOfAnimalWelfareFarms() != null && dairyFieldData.getNoOfAnimalWelfareFarms() < 0) {
            validationErrors.put("No of Animal Welfare Farms", "No of Animal Welfare Farms must be a positive number");
        }

        // Validate that Women Empowerment is a positive number
        if (dairyFieldData.getNoOfAnimalWelfareFarms() != null && dairyFieldData.getNoOfWomenEmpowerment() < 0) {
            validationErrors.put("No of Women Empowerment", "No of Women Empowerment must be a positive number");
        }

    }

    @Override
    public boolean isRequiredField(String fieldName) {
        return REQUIRED_FIELDS.contains(fieldName);
    }

    @Override
    public Set<String> getRequiredFields() {
        return REQUIRED_FIELDS;
    }
}
