package org.technoserve.udp.service;


import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.technoserve.udp.dto.ApiResponse;
import org.technoserve.udp.dto.FundCodeRequest;
import org.technoserve.udp.dto.FundCodesResponse;
import org.technoserve.udp.entity.common.Status;
import org.technoserve.udp.entity.program.FundCodeEntity;
import org.technoserve.udp.entity.program.Program;
import org.technoserve.udp.entity.sponsor.Sponsor;
import org.technoserve.udp.exception.BadRequestException;
import org.technoserve.udp.exception.ResourceNotFoundException;
import org.technoserve.udp.repository.CurrencyRepository;
import org.technoserve.udp.repository.FundCodeRepository;
import org.technoserve.udp.repository.ProgramRepository;
import org.technoserve.udp.repository.SponsorRepository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class FundCodeService {

  private final FundCodeRepository fundCodeRepository;
  private final SponsorRepository sponsorRepository;
  private final ProgramRepository programRepository;
  private final CurrencyRepository currencyRepository;
  private final ModelMapper modelMapper;


  public ApiResponse createFundCode(FundCodeRequest fundCodeDTO) {
    // Fetch Program
    Program program = fetchProgram(fundCodeDTO.getProgramKey());

    // Validate unique Fund Code
    validateFundCodeUnique(fundCodeDTO.getFundCode(), fundCodeDTO.getProgramKey());

    // Validate currency code
    validateCurrencyCode(fundCodeDTO.getCurrency());

    // Fetch Sponsor
    Sponsor sponsor = fetchSponsor(fundCodeDTO.getSponsorKey());

    // Convert DTO to Entity and set Sponsor and Program
    FundCodeEntity entity = convertToEntity(fundCodeDTO);
    entity.setSponsor(sponsor);
    entity.setProgram(program);
    entity.setStatus(Status.CREATED);

    // Save Fund Code
    FundCodeEntity savedEntity = fundCodeRepository.save(entity);

    return new ApiResponse("Fund Code created successfully", savedEntity.getFundCodeId());
  }

  public ApiResponse updateFundCode(Long id, FundCodeRequest fundCodeDTO) {
    // Fetch Fund Code Entity
    FundCodeEntity fundCode = fundCodeRepository.findById(id)
        .orElseThrow(() -> new ResourceNotFoundException("Fund Code with id " + id + " not found"));

    // Check if Fund Code needs to be unique
    if (!fundCode.getFundCode().equals(fundCodeDTO.getFundCode())) {
      validateFundCodeUnique(fundCodeDTO.getFundCode(), fundCodeDTO.getProgramKey());
    }

    // Validate currency code
    validateCurrencyCode(fundCodeDTO.getCurrency());

    // Fetch Program and Sponsor
    Program program = fetchProgram(fundCodeDTO.getProgramKey());
    Sponsor sponsor = fetchSponsor(fundCodeDTO.getSponsorKey());

    // Convert DTO to Entity and set Sponsor and Program
    FundCodeEntity saveFundCode = convertToEntity(fundCodeDTO);
    saveFundCode.setFundCodeId(id);
    saveFundCode.setSponsor(sponsor);
    saveFundCode.setProgram(program);
    saveFundCode.setStatus(Status.CREATED);

    // Save updated Fund Code
    FundCodeEntity updatedEntity = fundCodeRepository.save(saveFundCode);

    return new ApiResponse("Fund code updated successfully", updatedEntity.getFundCodeId());
  }


  public void deleteFundCode(Long id) {
    // Fetch Fund Code Entity
    FundCodeEntity entity = fundCodeRepository.findById(id)
        .orElseThrow(() -> new ResourceNotFoundException("Fund Code with id " + id + " not found"));

    // Check if Fund Code is already deleted
    if (entity.getStatus() == Status.DELETED) {
      throw new BadRequestException("Fund Code is already deleted");
    }

    // Soft delete (update status to DELETED)
    entity.setStatus(Status.DELETED);
    fundCodeRepository.save(entity);
  }


  public FundCodesResponse listOfFundCodesByProgram(Long programId) {
    List<FundCodeEntity> fundCodeList = fundCodeRepository.findByProgramProgramIdAndStatus(programId, Status.CREATED);

    int totalContribution = fundCodeList.stream()
        .mapToInt(FundCodeEntity::getContribution)
        .sum();

    BigDecimal totalBudgetAllocation = fundCodeList.stream()
        .map(FundCodeEntity::getBudgetAllocation)
        .filter(Objects::nonNull)
        .reduce(BigDecimal.ZERO, BigDecimal::add);

    BigDecimal totalImplementationBudget = fundCodeList.stream()
        .map(FundCodeEntity::getImplementationBudget)
        .filter(Objects::nonNull)
        .reduce(BigDecimal.ZERO, BigDecimal::add);

    BigDecimal totalPmuBudget = fundCodeList.stream()
        .map(FundCodeEntity::getPmuBudget)
        .filter(Objects::nonNull)
        .reduce(BigDecimal.ZERO, BigDecimal::add);

    return FundCodesResponse.builder()
        .fundCodeList(fundCodeList)
        .totalContribution(totalContribution)
        .totalBudgetAllocation(totalBudgetAllocation)
        .totalImplementationBudget(totalImplementationBudget)
        .totalPmuBudget(totalPmuBudget)
        .build();
  }

  private Program fetchProgram(Long programId) {
    return programRepository.findById(programId)
        .orElseThrow(() -> new ResourceNotFoundException("Program not found for id " + programId));
  }

  private Sponsor fetchSponsor(Long sponsorId) {
    return sponsorRepository.findById(sponsorId)
        .orElseThrow(() -> new ResourceNotFoundException("Sponsor not found for id " + sponsorId));
  }

  private void validateFundCodeUnique(String fundCode, Long programId) {
    if (fundCodeRepository.existsByFundCodeAndProgram_ProgramIdAndStatus(fundCode, programId, Status.CREATED)) {
      throw new BadRequestException("Fund Code must be unique");
    }
  }

  /**
   * Validate that the currency code exists in the database
   *
   * @param currencyCode The currency code to validate
   * @throws BadRequestException if the currency code does not exist
   */
  private void validateCurrencyCode(String currencyCode) {
    if (currencyCode == null || currencyCode.isEmpty()) {
      throw new BadRequestException("Currency code cannot be empty");
    }

    if (!currencyRepository.existsById(currencyCode)) {
      throw new BadRequestException("Invalid currency code: " + currencyCode);
    }
  }

  public FundCodeEntity convertToEntity(FundCodeRequest request) {
    return modelMapper.map(request, FundCodeEntity.class);
  }
}

