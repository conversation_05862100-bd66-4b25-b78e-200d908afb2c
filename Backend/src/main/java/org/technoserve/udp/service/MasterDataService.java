package org.technoserve.udp.service;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.technoserve.udp.dto.ApiResponse;
import org.technoserve.udp.dto.CurrencyDTO;
import org.technoserve.udp.dto.MasterDistrictDTO;
import org.technoserve.udp.dto.MasterDistrictRequest;
import org.technoserve.udp.dto.MasterStateDTO;
import org.technoserve.udp.entity.common.Currency;
import org.technoserve.udp.entity.common.MasterDistrict;
import org.technoserve.udp.entity.common.MasterState;
import org.technoserve.udp.exception.ConflictException;
import org.technoserve.udp.exception.ResourceNotFoundException;
import org.technoserve.udp.repository.CurrencyRepository;
import org.technoserve.udp.repository.MasterDistrictRepository;
import org.technoserve.udp.repository.MasterStateRepository;

import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@Service
public class MasterDataService {

  private final MasterStateRepository stateRepository;
  private final MasterDistrictRepository districtRepository;
  private final CurrencyRepository currencyRepository;
  private final ModelMapper modelMapper;


  public List<MasterStateDTO> getStatesByCountry(String countryName) {
    List<MasterState> states = stateRepository.findByMasterCountry_CountryNameOrderByStateNameAsc(countryName);
    return states.stream()
        .map(state -> modelMapper.map(state, MasterStateDTO.class))
        .toList();
  }

  public List<MasterDistrictDTO> getDistrictsByState(Long stateId) {
    List<MasterDistrict> districts = districtRepository.findByMasterState_MasterStateIdOrderByDistrictNameAsc(stateId);
    return districts.stream()
        .map(district -> modelMapper.map(district, MasterDistrictDTO.class))
        .toList();
  }

  @Transactional
  public ApiResponse createDistrict(MasterDistrictRequest request) {
    MasterState masterState = stateRepository.findById(request.getMasterStateId())
        .orElseThrow(() -> new ResourceNotFoundException("State not found"));

    // Check if district name already exists (ignore case)
    Optional<MasterDistrict> existingDistrict = districtRepository
        .findByDistrictNameIgnoreCaseAndMasterState(request.getDistrictName(), masterState);

    if (existingDistrict.isPresent()) {
      throw new ConflictException("District already exists in this state!");
    }

    // Create and save new district
    MasterDistrict district = new MasterDistrict();
    district.setDistrictName(request.getDistrictName());
    district.setMasterState(masterState);
    MasterDistrict savedDistrict = districtRepository.save(district);

    return new ApiResponse( "District created successfully!", savedDistrict.getMasterDistrictId());
  }

  /**
   * Get all currencies
   *
   * @return List of CurrencyDTO objects
   */
  public List<CurrencyDTO> getAllCurrencies() {
    List<Currency> currencies = currencyRepository.findAllByOrderByCodeAsc();
    return currencies.stream()
        .map(currency -> modelMapper.map(currency, CurrencyDTO.class))
        .toList();
  }
}
