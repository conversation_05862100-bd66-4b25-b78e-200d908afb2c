package org.technoserve.udp.service.processor.validator;

import org.technoserve.udp.entity.dataflow.CottonFarmingData;
import org.technoserve.udp.repository.FarmerRepository;

import java.math.BigDecimal;
import java.time.Year;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Validator for CottonFarmingData entities.
 */
public class CottonFarmingDataValidator extends AbstractEntityValidator<CottonFarmingData> {

    // All fields marked @FieldInfo are required
    private static final Set<String> REQUIRED_FIELDS = Stream.of(
        "farmerId","year", "schoolGoingChildren",
        "earningMembers", "totalLandholding", "primaryCrop", "secondaryCrops",
        "nonOrganicCottonLand", "organicCottonLand", "yearsOrganicPractice", "certificationStatus",
        "irrigationSource", "cattleCount", "drinkingWaterSource", "preferredSellingPoint",
        "hasStorageSpace", "receivesAgroAdvisory", "receivedTraining", "membershipInOrg",
        "maintainsRecords", "annualHouseholdIncome", "primaryIncomeSource", "primaryIncomeAmount",
        "certificationCostPerAcre", "avgProductionPerAcre", "costOfCultivationPerAcre",
        "organicCottonQuantitySold", "sellingPricePerKg", "bioInputsCost", "pestManagementBioInputs",
        "bioFertilizerUsed", "pheromoneTrapsPerAcre", "pheromoneTrapsPrice",
        "yellowStickyTrapsPerAcre", "yellowStickyTrapsPrice",
        "blueStickyTrapsPerAcre", "blueStickyTrapsPrice",
        "birdPerchesPerAcre", "irrigationCostPerAcre", "irrigationCount", "irrigationMethod",
        "farmMachineryHired", "machineryHiringCost", "localLabourCostPerDay",
        "migrantLabourCostPerDay", "workersForSowing", "workersForHarvesting",
        "harvestingTime", "weedingMethod", "weedingCostPerAcre", "mulchingCostPerAcre",
        "tillageCount", "tillageCostPerAcre", "landPreparationCost", "organicCottonSeedRate",
        "organicCottonSeedVariety", "borderCrop", "interCrop", "coverCrop", "trapCrop",
        "mulchingUsed", "mulchingType", "storagePrecautions", "hiredVehicleForTransport",
        "transportationCostPerKg", "rejectedQuantity", "priceDiscoveryMechanism",
        "paymentTransactionType", "creditDays", "govtSchemeAvailed", "cropInsurance",
        "cropInsuranceCostPerAcre", "hasKCC", "hasActiveBankAccount", "cropRotationUsed",
        "rotationCrops", "waterTrackingDevices", "pumpCapacity", "bufferZone",
        "cropResidueUtilization", "workerPaymentMode", "wageGenderDifference",
        "labourRegister", "safetyKitForWorkers", "shelterAndWaterForWorkers",
        "lavatoryForWorkers", "womenInAgriOperations", "communityWaterHarvesting",
        "soilMoistureMeterUsed"
    ).collect(Collectors.toSet());

    // Fields that must be provided as 'Yes' or 'No'
    private static final Set<String> YES_NO_FIELDS = Set.of(
        "hasStorageSpace", "receivesAgroAdvisory", "receivedTraining",
        "membershipInOrg", "maintainsRecords", "farmMachineryHired",
        "mulchingUsed", "safetyKitForWorkers", "shelterAndWaterForWorkers",
        "lavatoryForWorkers", "womenInAgriOperations", "communityWaterHarvesting",
        "soilMoistureMeterUsed"
    );

    // Numeric fields that must be positive
    private static final Set<String> POSITIVE_NUMERIC_FIELDS = Stream.of(
        "malesInHousehold", "femalesInHousehold", "childrenInHousehold",
        "schoolGoingChildren", "earningMembers", "totalLandholding",
        "nonOrganicCottonLand", "organicCottonLand", "yearsOrganicPractice",
        "cattleCount", "annualHouseholdIncome", "primaryIncomeAmount",
        "certificationCostPerAcre", "avgProductionPerAcre",
        "costOfCultivationPerAcre", "organicCottonQuantitySold",
        "sellingPricePerKg", "bioInputsCost", "pheromoneTrapsPerAcre",
        "pheromoneTrapsPrice", "yellowStickyTrapsPerAcre", "yellowStickyTrapsPrice",
        "blueStickyTrapsPerAcre", "blueStickyTrapsPrice", "birdPerchesPerAcre",
        "irrigationCostPerAcre", "irrigationCount", "machineryHiringCost",
        "localLabourCostPerDay", "migrantLabourCostPerDay", "workersForSowing",
        "workersForHarvesting", "weedingCostPerAcre", "mulchingCostPerAcre",
        "tillageCount", "tillageCostPerAcre", "landPreparationCost",
        "organicCottonSeedRate", "transportationCostPerKg", "rejectedQuantity",
        "creditDays", "cropInsuranceCostPerAcre", "pumpCapacity"
    ).collect(Collectors.toSet());

    private final FarmerRepository farmerRepository;

    public CottonFarmingDataValidator(FarmerRepository farmerRepository) {
        this.farmerRepository = farmerRepository;
    }

    @Override
    public void validate(CottonFarmingData data, Map<String, String> errors) {
        // Check presence of all required fields
        REQUIRED_FIELDS.forEach(field -> {
            Object value = getFieldValue(data, field);
            if (value == null || (value instanceof String && ((String) value).isBlank())) {
                errors.put(getDisplayName(field), getDisplayName(field) + " is required");
            }
        });

        // Check Yes/No fields
        YES_NO_FIELDS.forEach(field -> {
            Object val = getFieldValue(data, field);
            if (val instanceof String) {
                String s = ((String) val).strip();
                if (!s.equalsIgnoreCase("Yes") && !s.equalsIgnoreCase("No")) {
                    errors.put(getDisplayName(field), getDisplayName(field) + " must be 'Yes' or 'No'");
                }
            }
        });

        // Check numeric positivity
        POSITIVE_NUMERIC_FIELDS.forEach(field -> {
            Object val = getFieldValue(data, field);
            if (val instanceof Number) {
                BigDecimal num = toBigDecimal(val);
                if (num.compareTo(BigDecimal.ZERO) < 0) {
                    errors.put(getDisplayName(field), getDisplayName(field) + " must be positive");
                }
            }
        });

    }

    @Override
    public void performCustomValidations(CottonFarmingData data, Map<String, String> errors) {
        // Farmer existence validation
        if (data.getFarmerId() != null && !data.getFarmerId().isBlank()) {
            boolean exists = farmerRepository
                .findByFarmerIdAndProgramIdAndPartnerId(
                    data.getFarmerId(), data.getProgramId(), data.getPartnerId())
                .isPresent();
            if (!exists) {
                errors.put("Farmer ID", "Farmer ID does not exist in the database");
            }
        }

        // Year range
        if (data.getYear() != null) {
            int year = data.getYear();
            int current = Year.now().getValue();
            if (year < 2010 || year > current) {
                errors.put("Year", "Year must be between 2010 and " + current);
            }
        }
    }

    // Utility to read a field by name via reflection
    private Object getFieldValue(CottonFarmingData data, String fieldName) {
        try {
            var field = CottonFarmingData.class.getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(data);
        } catch (Exception e) {
            return null;
        }
    }

    // Utility to convert Number to BigDecimal
    private BigDecimal toBigDecimal(Object val) {
        if (val instanceof BigDecimal) return (BigDecimal) val;
        return new BigDecimal(val.toString());
    }

    // Use annotation name or fallback to fieldName
    private String getDisplayName(String field) {
        try {
            var f = CottonFarmingData.class.getDeclaredField(field);
            var fi = f.getAnnotation(org.technoserve.udp.entity.common.FieldInfo.class);
            return fi != null && !fi.name().isBlank() ? fi.name() : field;
        } catch (Exception e) {
            return field;
        }
    }

    @Override
    public boolean isRequiredField(String fieldName) {
        return REQUIRED_FIELDS.contains(fieldName);
    }

    @Override
    public Set<String> getRequiredFields() {
        return REQUIRED_FIELDS;
    }
}
