package org.technoserve.udp.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.technoserve.udp.entity.common.Currency;

import java.util.List;

@Repository
public interface CurrencyRepository extends JpaRepository<Currency, String> {
    
    /**
     * Find all currencies ordered by code
     * 
     * @return List of currencies ordered by code
     */
    List<Currency> findAllByOrderByCodeAsc();
}
