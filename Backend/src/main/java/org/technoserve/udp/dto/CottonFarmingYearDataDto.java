package org.technoserve.udp.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * DTO for Cotton Farming Year Data - represents cotton farming data for a specific year
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CottonFarmingYearDataDto {
    
    // Year and basic household info
    private Integer year;
    private Integer malesInHousehold;
    private Integer femalesInHousehold;
    private Integer childrenInHousehold;
    private Integer schoolGoingChildren;
    private Integer earningMembers;
    
    // Land and crop info
    private Double totalLandholding;
    private String primaryCrop;
    private String secondaryCrops;
    private Double nonOrganicCottonLand;
    private Double organicCottonLand;
    private Integer yearsOrganicPractice;
    private String certificationStatus;
    
    // Infrastructure and resources
    private String irrigationSource;
    private Integer cattleCount;
    private String drinkingWaterSource;
    private String preferredSellingPoint;
    private String hasStorageSpace;
    
    // Training and advisory
    private String receivesAgroAdvisory;
    private String receivedTraining;
    private String membershipInOrg;
    private String maintainsRecords;
    
    // Income and financial info
    private BigDecimal annualHouseholdIncome;
    private String primaryIncomeSource;
    private BigDecimal primaryIncomeAmount;
    
    // Production and costs
    private BigDecimal certificationCostPerAcre;
    private Double avgProductionPerAcre;
    private BigDecimal costOfCultivationPerAcre;
    private Double organicCottonQuantitySold;
    private BigDecimal sellingPricePerKg;
    
    // Bio-inputs and pest management
    private BigDecimal bioInputsCost;
    private String pestManagementBioInputs;
    private String bioFertilizerUsed;
    private Integer pheromoneTrapsPerAcre;
    private Integer yellowStickyTrapsPerAcre;
    private Integer blueStickyTrapsPerAcre;
    private Integer birdPerchesPerAcre;
    
    // Irrigation
    private BigDecimal irrigationCostPerAcre;
    private Integer irrigationCount;
    private String irrigationMethod;
    
    // Machinery and labor
    private String farmMachineryHired;
    private BigDecimal machineryHiringCost;
    private BigDecimal localLabourCostPerDay;
    private BigDecimal migrantLabourCostPerDay;
    private Integer workersForSowing;
    private Integer workersForHarvesting;
    
    // Field operations
    private String harvestingTime;
    private String weedingMethod;
    private BigDecimal weedingCostPerAcre;
    private BigDecimal mulchingCostPerAcre;
    private Integer tillageCount;
    private BigDecimal tillageCostPerAcre;
    private BigDecimal landPreparationCost;
    
    // Seeds and crops
    private Double organicCottonSeedRate;
    private String organicCottonSeedVariety;
    private String borderCrop;
    private String interCrop;
    private String coverCrop;
    private String trapCrop;
    
    // Mulching and storage
    private String mulchingUsed;
    private String mulchingType;
    private String storagePrecautions;
    
    // Transportation and selling
    private String hiredVehicleForTransport;
    private BigDecimal transportationCostPerKg;
    private Double rejectedQuantity;
    private String priceDiscoveryMechanism;
    private String paymentTransactionType;
    private Integer creditDays;
    
    // Government schemes and insurance
    private String govtSchemeAvailed;
    private String cropInsurance;
    private BigDecimal cropInsuranceCostPerAcre;
    private String hasKCC;
    private String hasActiveBankAccount;
    
    // Crop management
    private String cropRotationUsed;
    private String rotationCrops;
    private String waterTrackingDevices;
    private Integer pumpCapacity;
    private String bufferZone;
    private String cropResidueUtilization;
    
    // Worker welfare
    private String workerPaymentMode;
    private String wageGenderDifference;
    private String labourRegister;
    private String safetyKitForWorkers;
    private String shelterAndWaterForWorkers;
    private String lavatoryForWorkers;
    private String womenInAgriOperations;
    
    // Environmental practices
    private String communityWaterHarvesting;
    private String soilMoistureMeterUsed;
    
    // Calculated fields
    private Integer totalHHMembers;
    private Double dependencyRatio;
    private Double genderRatio;
    private Double schoolAttendanceRate;
    private Double totalCottonLand;
    private Double organicPercent;
    private Double landUsedForCotton;
    private Double incomePerEarner;
    private Double ocIncome;
    private Double profitPerAcre;
    private Double totalCertificationCost;
    private Double totalPTCost;
    private Double totalYSTCost;
    private Double totalBSTCost;
    private Double totalPestMgmtCost;
    private Double totalLabourCost;
    private Double machineryCostTotal;
    private Double totalIrrigationCost;
    private Double irrigationFrequency;
}
