package org.technoserve.udp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FundCodeRequest {

  @NotBlank(message = "Fund code cannot be empty")
  private String fundCode;

  @NotNull(message = "Sponsor ID is required")
  @JsonProperty("sponsorId")
  private Long sponsorKey;

  @Min(value = 0, message = "Contribution cannot be negative")
  private int contribution;

  @DecimalMin(value = "0.0", inclusive = false, message = "Budget allocation must be greater than zero")
  private BigDecimal budgetAllocation;

  @DecimalMin(value = "0.0", inclusive = false, message = "Implementation budget must be greater than zero")
  private BigDecimal implementationBudget;

  @DecimalMin(value = "0.0", inclusive = false, message = "PMU budget must be greater than zero")
  private BigDecimal pmuBudget;

  @NotBlank(message = "Currency code cannot be empty")
  private String currency;

  @NotNull(message = "Program ID is required")
  @JsonProperty("programId")
  private Long programKey;

}
