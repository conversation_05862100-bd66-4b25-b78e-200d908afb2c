package org.technoserve.udp.entity.common;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Entity representing a Currency
 */
@Entity
@Table(name = "currency")
@Getter
@Setter
@NoArgsConstructor
public class Currency {

    @Id
    @Column(name = "code", length = 3)
    private String code;

}
