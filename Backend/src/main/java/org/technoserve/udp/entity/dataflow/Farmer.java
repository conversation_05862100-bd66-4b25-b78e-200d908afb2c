package org.technoserve.udp.entity.dataflow;

import jakarta.persistence.*;
import lombok.*;
import org.technoserve.udp.entity.common.FieldInfo;

import java.math.BigDecimal;

@Entity
@Table(name = "farmer")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@IdClass(FarmerId.class)
public class Farmer {

  @Id
  @Column(name = "farmer_id")
  @FieldInfo(name = "Farmer Id")
  private String farmerId;

  @Id
  @Column(name = "program_id")
  private Long programId;

  @Id
  @Column(name = "partner_id")
  private Long partnerId;

  @FieldInfo(name = "Farmer Tracenet Code")
  @Column(name = "farmer_tracenet_code")
  private String farmerTracenetCode;

  @FieldInfo(name="Center ID")
  @Column(name = "centre_id")
  private String centreId;

  @FieldInfo(name = "Farmer Name")
  @Column(name = "farmer_name")
  private String farmerName;

  @FieldInfo(name = "Age")
  @Column(name = "age")
  private Integer age;

  @FieldInfo(name = "Gender")
  @Column(name = "gender")
  private String gender;

  @FieldInfo(name = "State")
  @Column(name = "state")
  private String state;

  @FieldInfo(name = "District")
  @Column(name = "district")
  private String district;

  @FieldInfo(name = "Village")
  @Column(name = "village")
  private String village;

  @FieldInfo(name = "Country Code")
  @Column(name = "country_code")
  private String countryCode = "91";

  @FieldInfo(name = "Mobile Number")
  @Column(name = "mobile_number")
  private String mobileNumber;

  @FieldInfo(name = "Marital Status")
  @Column(name = "marital_status")
  private String maritalStatus;

  @FieldInfo(name = "Spouse Name")
  @Column(name = "spouse_name")
  private String spouseName;

  @FieldInfo(name = "Caste")
  @Column(name = "caste")
  private String caste;

  @FieldInfo(name = "Highest education")
  @Column(name = "highest_education")
  private String highestEducation;

  @FieldInfo(name = "House Hold Size")
  @Column(name = "house_hold_size")
  private Integer houseHoldSize;

  @FieldInfo(name = "Land Size Under Cultivation")
  @Column(name = "land_size_under_cultivation")
  private Double landSizeUnderCultivation;

  @FieldInfo(name = "Land Measure Type")
  @Column(name = "land_measure_type")
  private String landMeasureType = "Acre";

  @FieldInfo(name = "Organic Status")
  @Column(name = "organic_status")
  private String organicStatus;

  @FieldInfo(name = "Herd Size")
  @Column(name = "herd_size")
  private Integer herdSize;

  @FieldInfo(name = "Any Other Income Generating Activity")
  @Column(name = "any_other_income_generating_activity")
  private String anyOtherIncomeGeneratingActivity;

  @FieldInfo(name = "Household Annual Income")
  @Column(name = "household_annual_income")
  private BigDecimal householdAnnualIncome;

  @FieldInfo(name = "Agricultural Annual Income")
  @Column(name = "agricultural_annual_income")
  private BigDecimal agriculturalAnnualIncome;

  @FieldInfo(name = "Dairy Annual Income")
  @Column(name = "dairy_annual_income")
  private BigDecimal dairyAnnualIncome;

  @FieldInfo(name = "Other Annual Income")
  @Column(name = "other_annual_income")
  private BigDecimal otherAnnualIncome = BigDecimal.ZERO;

  @FieldInfo(name = "Crops Grown")
  @Column(name = "crops_grown")
  private String cropsGrown;

  @FieldInfo(name = "Cattle Breed Types")
  @Column(name = "cattle_breed_types")
  private String cattleBreedTypes;

  @FieldInfo(name = "Loan Amount")
  @Column(name = "loan_amount")
  private BigDecimal loanAmount;

  @FieldInfo(name = "Agricultural Loan")
  @Column(name = "agricultural_loan")
  private BigDecimal agriculturalLoan;

  @FieldInfo(name = "Dairy Loan")
  @Column(name = "dairy_loan")
  private BigDecimal dairyLoan;

  @FieldInfo(name = "Latitude and Longitudes")
  @Column(name = "lat_long")
  private String latLong;

  @ManyToOne
  @JoinColumn(name = "excel_file_meta_data_id")
  private ExcelFileMetaData excelFileMetaData;

}
