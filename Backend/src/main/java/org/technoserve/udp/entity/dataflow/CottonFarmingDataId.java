package org.technoserve.udp.entity.dataflow;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.technoserve.udp.entity.common.FieldInfo;

/**
 * Composite primary key for Centre entity
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class CottonFarmingDataId {

  @Id
  @Column(name = "farmer_id")
  private String farmerId;

  @Id
  @Column(name = "program_id")
  private Long programId;

  @Id
  @Column(name = "partner_id")
  private Long partnerId;

  @Id
  @Column(name = "year")
  private Integer year;





}
