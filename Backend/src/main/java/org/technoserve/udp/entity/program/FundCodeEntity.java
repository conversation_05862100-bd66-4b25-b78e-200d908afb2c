package org.technoserve.udp.entity.program;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.technoserve.udp.entity.common.AbstractEntity;
import org.technoserve.udp.entity.common.Status;
import org.technoserve.udp.entity.sponsor.Sponsor;

import java.math.BigDecimal;

@Entity
@Table(name="fund_code")
@Getter
@Setter
@NoArgsConstructor
public class FundCodeEntity extends AbstractEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "fund_code_id")
  private Long fundCodeId;

  @ManyToOne
  @JoinColumn(name = "sponsor_id")
  private Sponsor sponsor;

  @Column(name="fund_code")
  private String fundCode;

  @Column(name="contribution")
  private int contribution;

  @Column(name="budget_allocation")
  private BigDecimal budgetAllocation;

  @Column(name="implementation_budget")
  private BigDecimal implementationBudget;

  @Column(name="pmu_budget")
  private BigDecimal pmuBudget;

  @Column(name="currency")
  private String currency;

  @Enumerated(EnumType.STRING)
  @JdbcTypeCode(SqlTypes.NAMED_ENUM)
  @Column(name = "status")
  @JsonIgnore
  private Status status;

  @ManyToOne
  @JoinColumn(name = "program_id")
  @JsonBackReference
  private Program program;

}
